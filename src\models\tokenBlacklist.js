const mongoose = require('mongoose');

const tokenBlacklistSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true,
    index: true // Optimize token lookups
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true // Optimize user lookups
  },
  tokenHash: {
    type: String,
    required: true,
    unique: true
  },
  reason: {
    type: String,
    enum: ['logout', 'reset', 'revoked'],
    default: 'logout'
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 86400 // 24 hours TTL
  },
  expiresAt: {
    type: Date,
    required: true,
    index: true // Optimize expiry lookups
  },
  metadata: {
    userAgent: String,
    ipAddress: String,
    location: String
  }
});

// Compound index for faster queries
tokenBlacklistSchema.index({ userId: 1, createdAt: 1 });

module.exports = mongoose.model('TokenBlacklist', tokenBlacklistSchema); 