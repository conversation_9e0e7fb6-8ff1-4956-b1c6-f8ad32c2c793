# Phone Verification Validation Implementation

## Overview

This document describes the implementation of phone verification validation logic that ensures users must complete email registration/login before accessing phone-based verification features.

## Problem Statement

The original phone verification system allowed users to create new accounts directly through phone verification, bypassing the primary email authentication flow. This implementation adds validation to ensure users establish their account through email authentication first.

## Implementation Details

### 1. Modified `sendPhoneCode` Method

**File**: `src/api/v1/controllers/oauthController.js`

**Changes**:
- Added user existence validation before sending SMS
- Query database for existing user with the provided phone number
- Return error if user doesn't exist
- Check user account status (blocked/banned users rejected)

**Validation Logic**:
```javascript
const existingUser = await User.findOne({ 
  phone_number: phoneNumber,
  status: { $in: ['active', 'inactive'] }
});

if (!existingUser) {
  return res.status(403).json({ 
    message: 'Please complete email registration/login first before using phone verification.',
    error_code: 'USER_NOT_FOUND'
  });
}
```

### 2. Modified `verifyPhoneCode` Method

**File**: `src/api/v1/controllers/oauthController.js`

**Changes**:
- Removed automatic user creation logic
- Added user existence validation before completing verification
- Check by both phone number and Firebase UID
- Update existing user's Firebase UID if needed
- Prevent new user creation entirely

**Validation Logic**:
```javascript
// Check by phone number first
let user = await User.findOne({ 
  phone_number: phoneNumber,
  status: { $in: ['active', 'inactive'] }
});

// Fallback to Firebase UID check
if (!user) {
  user = await User.findOne({ 
    firebase_uid: decodedToken.uid,
    status: { $in: ['active', 'inactive'] }
  });
}

// Reject if no user found
if (!user) {
  return res.status(403).json({ 
    message: 'Please complete email registration/login first before using phone verification.',
    error_code: 'USER_NOT_FOUND'
  });
}
```

### 3. Database Optimization

**File**: `src/models/User.js`

**Changes**:
- Added sparse index on `phone_number` field for improved query performance

```javascript
userSchema.index({ phone_number: 1 }, { sparse: true });
```

### 4. Updated API Documentation

**File**: `src/api/v1/routes/oauth.js`

**Changes**:
- Updated Swagger documentation to reflect new validation requirements
- Added clear descriptions about user existence requirements
- Documented new error responses (403 with USER_NOT_FOUND)
- Added examples of error responses

## Error Responses

### User Not Found (403)
```json
{
  "message": "Please complete email registration/login first before using phone verification.",
  "error_code": "USER_NOT_FOUND"
}
```

### Account Restricted (403)
```json
{
  "message": "Account is blocked. Please contact support.",
  "error_code": "ACCOUNT_RESTRICTED"
}
```

## Required User Flow

### 1. Primary Registration (Email-based)
Users must first create an account through one of these methods:
- Email OTP verification (`/api/v1/oauth/email/send-otp` → `/api/v1/oauth/email/verify-otp`)
- OAuth providers (Google, LinkedIn)
- Direct email registration

### 2. Phone Number Association
After account creation, users can add their phone number to their profile through:
- User profile update endpoints
- Account settings

### 3. Phone Verification
Only after steps 1 and 2 can users use phone verification:
- `/api/v1/oauth/phone/send-code` (validates user exists)
- `/api/v1/oauth/phone/verify-code` (completes verification for existing user)

## Security Benefits

1. **Prevents Account Bypass**: Users cannot skip email verification by using phone verification
2. **Maintains Data Integrity**: All users have verified email addresses
3. **Audit Trail**: Clear logging of validation failures
4. **Account Status Enforcement**: Blocked/banned users cannot use phone verification

## Logging and Monitoring

### Success Events
- `send_phone_code`: Phone verification code sent to existing user
- `verify_phone_code_success`: Phone verification completed for existing user
- `phone_verification_user_update`: User profile updated with Firebase UID/phone

### Warning Events
- `send_phone_code_user_not_found`: Phone verification attempted for non-existent user
- `verify_phone_code_user_not_found`: Code verification attempted for non-existent user
- `send_phone_code_user_blocked`: Phone verification attempted for blocked user
- `verify_phone_code_user_blocked`: Code verification attempted for blocked user

## Testing

A comprehensive test script is provided: `test-phone-verification-validation.js`

**Test Scenarios**:
1. Phone verification for non-existent user (should fail)
2. Phone verification for existing user (should succeed)
3. Invalid phone number format (should fail)
4. Missing phone number (should fail)
5. Code verification for non-existent user (should fail)

## Deployment Considerations

1. **Existing Users**: Users who previously used phone verification may need to complete email verification
2. **Migration**: Consider running a migration to ensure all users have email addresses
3. **Client Updates**: Frontend applications need to handle new error responses
4. **Documentation**: Update API documentation and user guides

## Configuration

No additional configuration required. The validation uses existing:
- User model and database queries
- Firebase authentication
- Existing error handling patterns

## Backward Compatibility

This is a **breaking change** for clients that relied on phone verification creating new users. Clients must be updated to:

1. Handle 403 responses with USER_NOT_FOUND error code
2. Guide users to complete email registration first
3. Update user flows to follow the new required sequence

## Performance Impact

- **Minimal**: Added database queries are optimized with sparse indexing
- **Improved**: Phone number queries now use dedicated index
- **Logging**: Additional log entries for validation events (minimal overhead)
