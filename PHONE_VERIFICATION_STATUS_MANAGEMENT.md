# Phone Verification Status Management Implementation

## Overview

This document describes the implementation of automatic phone verification status management in the user update API endpoint. The system now automatically manages a `phone_verified` field based on phone number presence and changes, ensuring users must re-verify their phone numbers when they change them.

## New Field: `phone_verified`

### Field Definition
```javascript
phone_verified: { 
  type: <PERSON>olean, 
  default: null, // null = no phone number, false = phone not verified, true = phone verified
  validate: {
    validator: function(value) {
      // If phone_number is null/empty, phone_verified should be null
      // If phone_number exists, phone_verified can be true or false
      if (!this.phone_number && value !== null) {
        return false;
      }
      return true;
    },
    message: 'phone_verified must be null when no phone number is set'
  }
}
```

### Field States
- **`null`**: User has no phone number
- **`false`**: User has a phone number but it's not verified
- **`true`**: User has a verified phone number

## Automatic Status Management Logic

### In User Update API (`PUT /api/v1/users/update_user`)

The system automatically manages the `phone_verified` status based on phone number changes:

#### 1. Phone Number Added
```javascript
// Before: phone_number = null, phone_verified = null
// After:  phone_number = "+15551234567", phone_verified = false
```
**Behavior**: When a user adds a phone number, `phone_verified` is set to `false` (requires verification).

#### 2. Phone Number Changed
```javascript
// Before: phone_number = "+15551234567", phone_verified = true
// After:  phone_number = "+15559876543", phone_verified = false
```
**Behavior**: When a user changes their phone number, `phone_verified` is reset to `false` (requires re-verification).

#### 3. Phone Number Removed
```javascript
// Before: phone_number = "+15551234567", phone_verified = true/false
// After:  phone_number = null, phone_verified = null
```
**Behavior**: When a user removes their phone number, `phone_verified` is set to `null`.

#### 4. Phone Number Unchanged
```javascript
// Before: phone_number = "+15551234567", phone_verified = true
// After:  phone_number = "+15551234567", phone_verified = true (preserved)
```
**Behavior**: When other fields are updated but phone number remains the same, `phone_verified` status is preserved.

## Implementation Details

### User Model Changes
**File**: `src/models/User.js`

- Added `phone_verified` field with validation
- Added sparse index on `phone_number` for performance
- Field validation ensures consistency between `phone_number` and `phone_verified`

### User Controller Changes
**File**: `src/api/v1/controllers/userController.js`

#### Enhanced `updateUserProfile` Method
```javascript
// Handle phone number and verification status management
if (phone_number !== undefined) {
  const currentPhoneNumber = user.phone_number;
  const newPhoneNumber = phone_number && phone_number.trim() !== '' ? phone_number : null;
  
  // Update phone number
  user.phone_number = newPhoneNumber;
  
  // Manage phone verification status
  if (newPhoneNumber === null) {
    // Phone number removed - set verification status to null
    user.phone_verified = null;
  } else if (currentPhoneNumber !== newPhoneNumber) {
    // Phone number changed - require re-verification
    user.phone_verified = false;
  }
  // If phone number unchanged, don't modify phone_verified status
}
```

#### Updated Response Objects
- `updateUserProfile`: Includes `phone_verified` in response
- `getCurrentUser`: Includes `phone_verified` in response

### Phone Verification Integration
**File**: `src/api/v1/controllers/oauthController.js`

#### Enhanced `verifyPhoneCode` Method
```javascript
// Mark phone as verified since verification was successful
if (user.phone_verified !== true) {
  user.phone_verified = true;
  userUpdated = true;
}
```

**Behavior**: When phone verification is successful, `phone_verified` is automatically set to `true`.

## API Documentation Updates

### Swagger Documentation
**File**: `src/api/v1/routes/users.js`

#### Request Body Documentation
```yaml
phone_number:
  type: string
  description: |
    User's phone number in E.164 format (optional).
    **Phone Verification Status Management:**
    - If phone number is changed, phone_verified will be set to false (requires re-verification)
    - If phone number is removed (set to null/empty), phone_verified will be set to null
    - If phone number remains unchanged, phone_verified status is preserved
  example: "+1234567890"
```

#### Response Schema Documentation
```yaml
phone_verified: { 
  type: boolean, 
  nullable: true,
  description: "Phone verification status: null (no phone), false (not verified), true (verified)"
}
```

## Logging and Monitoring

### New Log Events

#### Phone Number Removed
```javascript
logger.info('Phone number removed, verification status reset', {
  component: 'user-controller',
  operation: 'update_user_phone_removed',
  metadata: { 
    user_id: user._id,
    previous_phone: currentPhoneNumber?.substring(0, 5) + '***' || 'none'
  }
});
```

#### Phone Number Changed
```javascript
logger.info('Phone number changed, verification status reset', {
  component: 'user-controller',
  operation: 'update_user_phone_changed',
  metadata: { 
    user_id: user._id,
    previous_phone: currentPhoneNumber?.substring(0, 5) + '***' || 'none',
    new_phone: newPhoneNumber.substring(0, 5) + '***'
  }
});
```

#### Phone Verification Completed
```javascript
logger.info('User updated with Firebase UID, phone number, and verification status', {
  component: 'oauth-controller',
  operation: 'phone_verification_user_update',
  metadata: {
    user_id: user._id,
    phone_number: phoneNumber.substring(0, 5) + '***',
    phone_verified: true
  }
});
```

## Integration with Existing Phone Verification System

### Seamless Integration
The new `phone_verified` field integrates seamlessly with the existing phone verification validation system:

1. **User Registration**: Users must complete email registration first
2. **Phone Number Addition**: Users add phone number via profile update (`phone_verified = false`)
3. **Phone Verification**: Users can now use phone verification (`phone_verified = true` on success)
4. **Phone Number Changes**: Users must re-verify if they change their phone number

### Validation Flow
```
Email Registration → Add Phone Number → Phone Verification → Verified User
     (required)      (phone_verified=false)  (phone_verified=true)
```

## Error Handling

### Validation Errors
- **Invalid phone format**: Returns 400 with field-specific error
- **Phone verification consistency**: Model validation ensures data integrity

### Example Error Response
```json
{
  "message": "Phone number must be in E.164 format (e.g., +1234567890)",
  "field": "phone_number"
}
```

## Testing

### Comprehensive Test Suite
**File**: `test-phone-verification-status.js`

**Test Scenarios**:
1. Adding phone number (sets `phone_verified = false`)
2. Changing phone number (resets `phone_verified = false`)
3. Updating other fields (preserves `phone_verified` status)
4. Removing phone number (sets `phone_verified = null`)
5. Phone number format validation
6. Integration with phone verification endpoints

## Security Benefits

### Enhanced Security
1. **Re-verification Required**: Users must verify new phone numbers
2. **Clear Status Tracking**: Always know verification status
3. **Prevents Bypass**: Can't skip verification by changing phone numbers
4. **Audit Trail**: Complete logging of phone number and verification changes

### Data Integrity
1. **Consistent State**: Phone number and verification status always match
2. **Validation Rules**: Model-level validation prevents inconsistent data
3. **Automatic Management**: Reduces human error in status management

## Migration Considerations

### Existing Users
- Existing users with phone numbers will have `phone_verified = null` initially
- They will need to verify their phone numbers to set `phone_verified = true`
- No breaking changes to existing API contracts

### Database Migration
```javascript
// Optional migration script to set initial phone_verified values
db.users.updateMany(
  { phone_number: { $exists: true, $ne: null } },
  { $set: { phone_verified: false } }
);

db.users.updateMany(
  { phone_number: { $in: [null, ""] } },
  { $set: { phone_verified: null } }
);
```

## Performance Impact

### Minimal Overhead
- **Database**: Single field addition with sparse indexing
- **API**: Minimal processing overhead for status management
- **Queries**: Existing phone number index supports new queries

### Optimizations
- Sparse index on `phone_number` for efficient lookups
- Conditional updates only when phone number changes
- Efficient logging with masked phone numbers

## Future Enhancements

### Potential Improvements
1. **Verification Timestamps**: Track when phone was last verified
2. **Verification Methods**: Support multiple verification methods
3. **Expiration**: Add verification expiration dates
4. **Batch Operations**: Support bulk phone verification status updates

### API Extensions
1. **Phone Verification History**: Endpoint to view verification history
2. **Force Re-verification**: Admin endpoint to force re-verification
3. **Verification Reminders**: Automated reminders for unverified phones
