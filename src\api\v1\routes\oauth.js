//src/api/v1/routes/oauth.js
require('dotenv').config();
const express = require('express');
const router = express.Router();
const oauthController = require('../controllers/oauthController');

/**
 * @swagger
 * tags:
 *   name: Otp-Auth
 *   description: Email OTP authentication flows
 */
/**
 * @swagger
 * /api/v1/oauth/email/send-otp:
 *   post:
 *     summary: Send a one-time verification code (OTP) to an email address
 *     tags: [Otp-Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: The email address to which the OTP will be sent
 *             example:
 *               email: "<EMAIL>"
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: otp_sent
 *       400:
 *         description: Email is required or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: email_required
 *       500:
 *         description: Error while sending OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: error_sending_otp
 *                 error:
 *                   type: string
 *                   example: SMTP error or other error details
 */
router.post('/email/send-otp', oauthController.sendEmailOtp);

/**
 * @swagger
 * /api/v1/oauth/email/verify-otp:
 *   post:
 *     summary: Verify the email OTP and sign in via Firebase Auth
 *     tags: [Otp-Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [email, code]
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               code:
 *                 type: string
 *                 description: 6-digit verification code
 *             example:
 *               email: "<EMAIL>"
 *               code: "123456"
 *     responses:
 *       200:
 *         description: Sign-in successful, returns Firebase tokens
 *       400:
 *         description: Missing or invalid OTP
 *       500:
 *         description: Error verifying OTP
 */
router.post('/email/verify-otp', oauthController.verifyEmailOtp);

/**
 * @swagger
 * /api/v1/oauth/phone/send-code:
 *   post:
 *     summary: Send SMS verification code for phone sign-in (requires existing user account)
 *     tags: [Otp-Auth]
 *     description: |
 *       Sends an SMS verification code to the provided phone number.
 *       **Important**: The user must already exist in the database with the provided phone number.
 *       Users must complete email registration/login first before using phone verification.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [phoneNumber, recaptchaToken]
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 description: E.164 phone number (e.g. +***********) - must belong to an existing user
 *               recaptchaToken:
 *                 type: string
 *             example:
 *               phoneNumber: "+***********"
 *               recaptchaToken: "03AGdBq25"
 *     responses:
 *       200:
 *         description: SMS code sent, returns sessionInfo
 *       400:
 *         description: Phone number and reCAPTCHA token required
 *       402:
 *         description: Billing not enabled
 *       403:
 *         description: User not found - complete email registration first
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Please complete email registration/login first before using phone verification."
 *                 error_code:
 *                   type: string
 *                   example: "USER_NOT_FOUND"
 *       500:
 *         description: Error sending OTP
 */
router.post('/phone/send-code', oauthController.sendPhoneCode);

/**
 * @swagger
 * /api/v1/oauth/phone/verify-code:
 *   post:
 *     summary: Verify SMS code and sign in via Firebase Auth (requires existing user account)
 *     tags: [Otp-Auth]
 *     description: |
 *       Verifies the SMS code and completes phone-based authentication.
 *       **Important**: The user must already exist in the database. This endpoint will not create new users.
 *       Users must complete email registration/login first before using phone verification.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [sessionInfo, code]
 *             properties:
 *               sessionInfo:
 *                 type: string
 *                 description: Session info returned from send-code endpoint
 *               code:
 *                 type: string
 *                 description: 6-digit SMS verification code
 *             example:
 *               sessionInfo: "SESSION_INFO"
 *               code: "123456"
 *     responses:
 *       200:
 *         description: Signed in successfully, returns Firebase tokens and user info
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 idToken:
 *                   type: string
 *                 refreshToken:
 *                   type: string
 *                 expiresIn:
 *                   type: string
 *                 phoneNumber:
 *                   type: string
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     firebase_uid:
 *                       type: string
 *                     phone_number:
 *                       type: string
 *                     name:
 *                       type: string
 *                     status:
 *                       type: string
 *                     email:
 *                       type: string
 *       400:
 *         description: Session info and code required
 *       401:
 *         description: Invalid OTP
 *       403:
 *         description: User not found - complete email registration first
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Please complete email registration/login first before using phone verification."
 *                 error_code:
 *                   type: string
 *                   example: "USER_NOT_FOUND"
 *       500:
 *         description: Verification failed
 */
router.post('/phone/verify-code', oauthController.verifyPhoneCode);

/**
 * @swagger
 * tags:
 *   name: OAuth
 *   description: OAuth endpoints for Google & LinkedIn via Firebase
 */

/**
 * @swagger
 * /api/v1/oauth/{provider}/login:
 *   get:
 *     summary: Redirect to OAuth provider consent screen
 *     tags: [OAuth]
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum: [google, linkedin]
 *     responses:
 *       302:
 *         description: Redirect to provider’s consent page
 *       400:
 *         description: Unsupported provider
 */
router.get('/google/login', oauthController.getGoogleOAuthUrl);

/**
 * @swagger
 * /api/v1/oauth/{provider}/callback:
 *   get:
 *     summary: Handle OAuth callback and sign in via Firebase
 *     tags: [OAuth]
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum:
 *             - google
 *             - linkedin
 *         description: The OAuth provider
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: Authorization code returned by the provider
 *       - in: query
 *         name: state
 *         required: true
 *         schema:
 *           type: string
 *         description: Echoed state to validate the response
 *     responses:
 *       200:
 *         description: Returns Firebase ID token and refresh token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 idToken:
 *                   type: string
 *                 refreshToken:
 *                   type: string
 *                 expiresIn:
 *                   type: string
 *                 email:
 *                   type: string
 *                 localId:
 *                   type: string
 *       400:
 *         description: Missing code or invalid state
 *       500:
 *         description: OAuth callback failure
 */
router.get('/google/callback', oauthController.handleGoogleCallback);

module.exports = router;
