/**
 * Test script for phone verification validation
 * This script tests the new validation logic that requires users to exist before phone verification
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/v1`;

// Test configuration
const TEST_PHONE_NUMBER = '+15551234567'; // Use a valid E.164 format phone number
const TEST_EMAIL = '<EMAIL>';
const RECAPTCHA_TOKEN = 'test-recaptcha-token'; // In production, use real reCAPTCHA

async function testPhoneVerificationValidation() {
  console.log('🧪 Testing Phone Verification Validation Logic\n');

  try {
    // Test 1: Try to send phone verification code for non-existent user
    console.log('📱 Test 1: Attempting phone verification for non-existent user...');
    try {
      const response = await axios.post(`${API_BASE}/oauth/phone/send-code`, {
        phoneNumber: TEST_PHONE_NUMBER,
        recaptchaToken: RECAPTCHA_TOKEN
      });
      
      console.log('❌ FAIL: Phone verification should have been rejected for non-existent user');
      console.log('Response:', response.data);
    } catch (error) {
      if (error.response?.status === 403 && error.response?.data?.error_code === 'USER_NOT_FOUND') {
        console.log('✅ PASS: Phone verification correctly rejected for non-existent user');
        console.log('Message:', error.response.data.message);
      } else {
        console.log('❌ FAIL: Unexpected error response');
        console.log('Status:', error.response?.status);
        console.log('Data:', error.response?.data);
      }
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 2: Create a user first, then try phone verification
    console.log('📧 Test 2: Creating user via email registration...');
    try {
      // First create a user via email OTP (simulating the required flow)
      const emailOtpResponse = await axios.post(`${API_BASE}/oauth/email/send-otp`, {
        email: TEST_EMAIL
      });
      
      console.log('✅ Email OTP sent successfully');
      console.log('Now you would need to verify the email OTP to create the user account');
      console.log('After that, you can add the phone number to the user profile');
      console.log('Then phone verification would work');
      
    } catch (error) {
      console.log('⚠️  Email OTP sending failed (this might be expected in test environment)');
      console.log('Error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 3: Test with invalid phone number format
    console.log('📱 Test 3: Testing with invalid phone number format...');
    try {
      const response = await axios.post(`${API_BASE}/oauth/phone/send-code`, {
        phoneNumber: 'invalid-phone',
        recaptchaToken: RECAPTCHA_TOKEN
      });
      
      console.log('❌ FAIL: Should have rejected invalid phone number');
    } catch (error) {
      console.log('✅ PASS: Invalid phone number correctly rejected');
      console.log('Status:', error.response?.status);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 4: Test without phone number
    console.log('📱 Test 4: Testing without phone number...');
    try {
      const response = await axios.post(`${API_BASE}/oauth/phone/send-code`, {
        recaptchaToken: RECAPTCHA_TOKEN
      });
      
      console.log('❌ FAIL: Should have rejected missing phone number');
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message === 'phone_number_required') {
        console.log('✅ PASS: Missing phone number correctly rejected');
      } else {
        console.log('❌ FAIL: Unexpected error for missing phone number');
        console.log('Response:', error.response?.data);
      }
    }

  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  }
}

async function testPhoneVerificationCodeValidation() {
  console.log('\n🔐 Testing Phone Verification Code Validation\n');

  try {
    // Test verify-code endpoint with non-existent user scenario
    console.log('📱 Test: Attempting to verify code for non-existent user...');
    try {
      const response = await axios.post(`${API_BASE}/oauth/phone/verify-code`, {
        sessionInfo: 'fake-session-info',
        code: '123456'
      });
      
      console.log('❌ FAIL: Code verification should have been rejected');
    } catch (error) {
      if (error.response?.status === 403 && error.response?.data?.error_code === 'USER_NOT_FOUND') {
        console.log('✅ PASS: Code verification correctly rejected for non-existent user');
      } else if (error.response?.status === 401) {
        console.log('✅ PASS: Invalid session/code correctly rejected');
      } else {
        console.log('⚠️  Unexpected response (might be Firebase validation)');
        console.log('Status:', error.response?.status);
        console.log('Data:', error.response?.data);
      }
    }

  } catch (error) {
    console.error('❌ Code verification test failed:', error.message);
  }
}

// Helper function to display test summary
function displayTestSummary() {
  console.log('\n' + '='.repeat(80));
  console.log('📋 PHONE VERIFICATION VALIDATION TEST SUMMARY');
  console.log('='.repeat(80));
  console.log('✅ Validation Logic Implemented:');
  console.log('   • Phone verification requires existing user account');
  console.log('   • Users must complete email registration first');
  console.log('   • Clear error messages guide users to proper flow');
  console.log('   • Account status validation (blocked/banned users rejected)');
  console.log('   • Phone number indexing for performance');
  console.log('');
  console.log('📚 Implementation Details:');
  console.log('   • sendPhoneCode: Validates user existence before sending SMS');
  console.log('   • verifyPhoneCode: Prevents new user creation, validates existing users');
  console.log('   • Error codes: USER_NOT_FOUND, ACCOUNT_RESTRICTED');
  console.log('   • Updated Swagger documentation with new requirements');
  console.log('');
  console.log('🔧 Next Steps:');
  console.log('   1. Test with real user accounts in development environment');
  console.log('   2. Verify phone number can be added to existing user profiles');
  console.log('   3. Test the complete flow: email signup → add phone → phone verification');
  console.log('   4. Monitor logs for validation events');
  console.log('='.repeat(80));
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Phone Verification Validation Tests...\n');
  
  await testPhoneVerificationValidation();
  await testPhoneVerificationCodeValidation();
  
  displayTestSummary();
}

// Execute if run directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testPhoneVerificationValidation,
  testPhoneVerificationCodeValidation,
  runAllTests
};
