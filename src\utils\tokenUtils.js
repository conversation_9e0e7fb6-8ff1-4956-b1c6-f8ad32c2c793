/**
 * Extract JWT token from request
 * @param {Object} req - Express request object
 * @returns {string|null} - The token or null if not found
 */
const extractToken = (req) => {
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
    return req.headers.authorization.split(' ')[1];
  }
  if (req.cookies && req.cookies.jwt) {
    return req.cookies.jwt;
  }
  return null;
};

module.exports = {
  extractToken
}; 