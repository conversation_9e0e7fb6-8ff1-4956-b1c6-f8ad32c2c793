const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
require('dotenv').config();

const tags = [
  { name: 'Users', description: 'User management and profile operations' },
  { name: 'Config', description: 'Runtime configuration settings (admin only)' },
  { name: 'Auth', description: 'Authentication flows including login, registration, and password reset' },
  { name: 'Otp-Auth', description: 'One-time password authentication via email and phone' },
  { name: 'OAuth', description: 'OAuth authentication with third-party providers (Google, LinkedIn)' },
  { name: 'Middleware', description: 'Utility endpoints for token refresh and authentication' },
  {name: 'Organizations', description: 'Organization management and profile operations' },
  { name: 'Health', description: 'Service health and status checks' }
];

const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Auth DIGGI API',
      version: '1.0.0',
      description: 'Authentication API with Firebase Auth and MongoDB integration, supporting multiple authentication methods including email/password, phone OTP, and OAuth providers.',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
        url: 'https://example.com/support'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.SERVER_URL,
        description: 'API Server'
      }
    ],
    tags,
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Pass your Firebase ID token as: `Authorization: Bearer <token>`'
        },
        OAuth2PasswordBearer: {
          type: 'oauth2',
          flows: {
            password: {
              tokenUrl: `${process.env.SERVER_URL}/api/v1/auth/login`,
              scopes: {}
            }
          }
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              description: 'MongoDB document ID'
            },
            firebase_uid: {
              type: 'string',
              description: 'Firebase User ID'
            },
            JWT_UID: {
              type: 'string',
              description: 'Custom JWT User ID'
            },
            name: {
              type: 'string',
              description: 'User\'s full name'
            },
            user_name: {
              type: 'string',
              description: 'Auto-generated unique username (URL-safe format)',
              example: 'john-doe-a7x9',
              pattern: '^[a-z][a-z0-9_-]*$',
              maxLength: 50
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User\'s email address'
            },
            phone_number: {
              type: 'string',
              description: 'User\'s phone number in E.164 format (e.g., +1234567890)'
            }
          }
        },
        Config: {
          type: 'object',
          properties: {
            smtp: {
              type: 'object',
              properties: {
                user: {
                  type: 'string',
                  description: 'SMTP username/email'
                },
                pass: {
                  type: 'string',
                  description: 'SMTP password or app-specific password'
                },
                fromAddress: {
                  type: 'string',
                  description: 'Email sender address'
                },
                cc_users: {
                  type: 'array',
                  items: {
                    type: 'string'
                  },
                  description: 'List of CC email addresses'
                },
                updatedAt: {
                  type: 'string',
                  format: 'date-time',
                  description: 'Last update timestamp'
                }
              }
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Error message'
            },
            error: {
              type: 'string',
              description: 'Detailed error information'
            }
          }
        },
        TokenResponse: {
          type: 'object',
          properties: {
            idToken: {
              type: 'string',
              description: 'Firebase ID token'
            },
            refreshToken: {
              type: 'string',
              description: 'Firebase refresh token'
            },
            expiresIn: {
              type: 'string',
              description: 'Token expiration time in seconds'
            }
          }
        },
        JwtTokenResponse: {
          type: 'object',
          properties: {
            access_token: {
              type: 'string',
              description: 'JWT access token'
            },
            refresh_token: {
              type: 'string',
              description: 'JWT refresh token'
            },
            expires_in: {
              type: 'number',
              description: 'Token expiration time in seconds'
            },
            token_type: {
              type: 'string',
              description: 'Token type (bearer)'
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication information is missing or invalid',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                message: 'invalid_token',
                error: 'Token has expired'
              }
            }
          }
        },
        NotFoundError: {
          description: 'The specified resource was not found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                message: 'user_not_found'
              }
            }
          }
        },
        ValidationError: {
          description: 'Invalid input parameters',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                message: 'validation_error',
                error: 'Email is required'
              }
            }
          }
        },
        ServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                message: 'server_error',
                error: 'Database connection failed'
              }
            }
          }
        }
      }
    },
    // Applies both auth types globally (use security: [] at route level to disable for any endpoint)
    security: [
      { bearerAuth: [] },
      { OAuth2PasswordBearer: [] }
    ]
  },
  apis: [
    './src/api/v1/routes/*.js',
    './src/middleware/*.js',
    './src/index.js',
    './src/models/*.js'
  ]
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);
module.exports = { swaggerSpec, swaggerUi };
