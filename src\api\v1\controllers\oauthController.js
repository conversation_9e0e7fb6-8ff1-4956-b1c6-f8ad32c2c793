//src/api/v1/controllers/oauthController.js
require('dotenv').config();
const axios = require('axios');
const admin = require('../../../config/firebase');
const { sendOtpEmail } = require('../../../utils/mailer');
const User = require('../../../models/User');
const logger = require('../../../services/logger');

const API = 'https://identitytoolkit.googleapis.com/v1';
const key = process.env.FIREBASE_API_KEY;
const SERVER_URL = process.env.SERVER_URL;

/**
 * OAuth Controller
 * Handles OAuth flows including Google OAuth and phone verification
 */
class OAuthController {

  /**
   * Get Google OAuth URL for authentication
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getGoogleOAuthUrl(req, res) {
    try {
      const subdomain = req.query.subdomain || req.headers['x-subdomain'] || '';
      const baseUrl = subdomain ? `https://${subdomain}.${process.env.DOMAIN}` : SERVER_URL;
      const redirectUri = `${baseUrl}/api/v1/oauth/google/callback`;

      const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `response_type=code&` +
        `scope=openid email profile&` +
        `state=${subdomain || 'main'}`;

      logger.info('Google OAuth URL generated', {
        component: 'oauth-controller',
        operation: 'get_google_oauth_url',
        metadata: {
          subdomain,
          redirect_uri: redirectUri,
          user_id: req.user?._id
        }
      });

      res.json({
        authUrl: googleAuthUrl,
        redirectUri,
        subdomain: subdomain || 'main'
      });
    } catch (err) {
      logger.error('Failed to generate Google OAuth URL', err, {
        component: 'oauth-controller',
        operation: 'get_google_oauth_url'
      });
      res.status(500).json({ 
        message: 'Failed to generate OAuth URL', 
        error: err.message 
      });
    }
  }

  /**
   * Handle Google OAuth callback
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async handleGoogleCallback(req, res) {
    const { code, state } = req.query;

    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    try {
      const subdomain = state !== 'main' ? state : '';
      const baseUrl = subdomain ? `https://${subdomain}.${process.env.DOMAIN}` : SERVER_URL;
      const redirectUri = `${baseUrl}/api/v1/oauth/google/callback`;

      // Exchange code for tokens
      const tokenResponse = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri
      });

      const { access_token, id_token } = tokenResponse.data;

      // Get user info from Google
      const userResponse = await axios.get(
        `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${access_token}`
      );

      const googleUser = userResponse.data;

      // Create or update Firebase user
      let firebaseUser;
      try {
        firebaseUser = await admin.auth().getUserByEmail(googleUser.email);
      } catch (err) {
        if (err.code === 'auth/user-not-found') {
          firebaseUser = await admin.auth().createUser({
            email: googleUser.email,
            displayName: googleUser.name,
            photoURL: googleUser.picture,
            emailVerified: googleUser.verified_email
          });
        } else {
          throw err;
        }
      }

      // Create or update MongoDB user
      let user = await User.findOne({ firebase_uid: firebaseUser.uid });
      if (!user) {
        user = await new User({
          firebase_uid: firebaseUser.uid,
          email: googleUser.email,
          name: googleUser.name,
          profile: {
            picture: googleUser.picture,
            locale: googleUser.locale
          },
          status: 'active'
        }).save();

        logger.info('New user created via Google OAuth', {
          component: 'oauth-controller',
          operation: 'google_callback_user_creation',
          metadata: {
            user_id: user._id,
            email: googleUser.email,
            subdomain
          }
        });
      }

      // Generate custom JWT for consistency
      const jwt = require('jsonwebtoken');
      const customToken = jwt.sign({ sub: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
      const refreshToken = jwt.sign({ sub: user._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });

      // Redirect to frontend with tokens
      const frontendUrl = subdomain 
        ? `https://${subdomain}.${process.env.DOMAIN}/auth/callback`
        : `${SERVER_URL}/auth/callback`;

      const redirectUrl = `${frontendUrl}?` +
        `access_token=${customToken}&` +
        `refresh_token=${refreshToken}&` +
        `expires_in=3600&` +
        `user_id=${user._id}`;

      logger.info('Google OAuth callback completed', {
        component: 'oauth-controller',
        operation: 'google_callback_success',
        metadata: {
          user_id: user._id,
          email: user.email,
          subdomain
        }
      });

      res.redirect(redirectUrl);
    } catch (err) {
      logger.error('Google OAuth callback failed', err, {
        component: 'oauth-controller',
        operation: 'google_callback_error',
        metadata: { code: code?.substring(0, 10), state }
      });

      const errorUrl = state !== 'main' 
        ? `https://${state}.${process.env.DOMAIN}/auth/error`
        : `${SERVER_URL}/auth/error`;
      
      res.redirect(`${errorUrl}?error=oauth_failed`);
    }
  }

  /**
   * Send phone verification code
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async sendPhoneCode(req, res) {
    const { phoneNumber, recaptchaToken } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ message: 'phone_number_required' });
    }

    try {
      // Check if user exists in database before sending SMS
      const existingUser = await User.findOne({
        phone_number: phoneNumber,
        status: { $in: ['active', 'inactive'] } // Allow active and inactive users
      });

      if (!existingUser) {
        logger.warn('Phone verification attempted for non-existent user', {
          component: 'oauth-controller',
          operation: 'send_phone_code_user_not_found',
          metadata: { phone_number: phoneNumber.substring(0, 5) + '***' }
        });

        return res.status(403).json({
          message: 'Please complete email registration/login first before using phone verification.',
          error_code: 'USER_NOT_FOUND'
        });
      }

      // Check if user account is blocked or banned
      if (existingUser.status === 'blocked' || existingUser.status === 'banned') {
        logger.warn('Phone verification attempted for blocked/banned user', {
          component: 'oauth-controller',
          operation: 'send_phone_code_user_blocked',
          metadata: {
            phone_number: phoneNumber.substring(0, 5) + '***',
            user_id: existingUser._id,
            status: existingUser.status
          }
        });

        return res.status(403).json({
          message: `Account is ${existingUser.status}. Please contact support.`,
          error_code: 'ACCOUNT_RESTRICTED'
        });
      }

      const response = await axios.post(
        `${API}/accounts:sendVerificationCode?key=${key}`,
        {
          phoneNumber,
          recaptchaToken
        }
      );

      logger.info('Phone verification code sent', {
        component: 'oauth-controller',
        operation: 'send_phone_code',
        metadata: {
          phone_number: phoneNumber.substring(0, 5) + '***',
          user_id: existingUser._id,
          session_info: response.data.sessionInfo?.substring(0, 10)
        }
      });

      res.json({
        sessionInfo: response.data.sessionInfo,
        message: 'verification_code_sent'
      });
    } catch (err) {
      logger.error('Failed to send phone verification code', err, {
        component: 'oauth-controller',
        operation: 'send_phone_code_error',
        metadata: { phone_number: phoneNumber?.substring(0, 5) + '***' }
      });
      res.status(400).json({
        message: 'failed_to_send_code',
        error: err.response?.data || err.message
      });
    }
  }

  /**
   * Verify phone code and complete authentication
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async verifyPhoneCode(req, res) {
    const { sessionInfo, code } = req.body;

    if (!sessionInfo || !code) {
      return res.status(400).json({ message: 'session_and_code_required' });
    }

    try {
      const response = await axios.post(
        `${API}/accounts:signInWithPhoneNumber?key=${key}`,
        { sessionInfo, code }
      );

      const { idToken, refreshToken, phoneNumber } = response.data;

      // Verify the Firebase token and get user info
      const decodedToken = await admin.auth().verifyIdToken(idToken);

      // Check if user exists in our database first by phone number
      let user = await User.findOne({
        phone_number: phoneNumber,
        status: { $in: ['active', 'inactive'] }
      });

      // If not found by phone, try by Firebase UID (for existing Firebase users)
      if (!user) {
        user = await User.findOne({
          firebase_uid: decodedToken.uid,
          status: { $in: ['active', 'inactive'] }
        });
      }

      // If still no user found, reject the verification
      if (!user) {
        logger.warn('Phone verification attempted for non-existent user', {
          component: 'oauth-controller',
          operation: 'verify_phone_code_user_not_found',
          metadata: {
            phone_number: phoneNumber.substring(0, 5) + '***',
            firebase_uid: decodedToken.uid
          }
        });

        return res.status(403).json({
          message: 'Please complete email registration/login first before using phone verification.',
          error_code: 'USER_NOT_FOUND'
        });
      }

      // Check if user account is blocked or banned
      if (user.status === 'blocked' || user.status === 'banned') {
        logger.warn('Phone verification attempted for blocked/banned user', {
          component: 'oauth-controller',
          operation: 'verify_phone_code_user_blocked',
          metadata: {
            phone_number: phoneNumber.substring(0, 5) + '***',
            user_id: user._id,
            status: user.status
          }
        });

        return res.status(403).json({
          message: `Account is ${user.status}. Please contact support.`,
          error_code: 'ACCOUNT_RESTRICTED'
        });
      }

      // Update user's Firebase UID and phone number if needed
      let userUpdated = false;
      if (!user.firebase_uid) {
        user.firebase_uid = decodedToken.uid;
        userUpdated = true;
      }
      if (!user.phone_number || user.phone_number !== phoneNumber) {
        user.phone_number = phoneNumber;
        userUpdated = true;
      }

      if (userUpdated) {
        await user.save();
        logger.info('User updated with Firebase UID and phone number', {
          component: 'oauth-controller',
          operation: 'phone_verification_user_update',
          metadata: {
            user_id: user._id,
            phone_number: phoneNumber.substring(0, 5) + '***'
          }
        });
      }

      logger.info('Phone verification completed', {
        component: 'oauth-controller',
        operation: 'verify_phone_code_success',
        metadata: {
          user_id: user._id,
          phone_number: phoneNumber.substring(0, 5) + '***'
        }
      });

      res.json({
        idToken,
        refreshToken,
        expiresIn: response.data.expiresIn,
        phoneNumber,
        user: {
          _id: user._id,
          firebase_uid: user.firebase_uid,
          phone_number: user.phone_number,
          name: user.name,
          status: user.status,
          email: user.email
        }
      });
    } catch (err) {
      logger.error('Phone verification failed', err, {
        component: 'oauth-controller',
        operation: 'verify_phone_code_error',
        metadata: { session_info: sessionInfo?.substring(0, 10) }
      });
      res.status(401).json({
        message: 'invalid_otp',
        error: err.response?.data || err.message
      });
    }
  }

  /**
   * Send email OTP for verification
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async sendEmailOtp(req, res) {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'email_required' });
    }

    try {
      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      
      // Store OTP in session or cache (simplified for demo)
      req.session = req.session || {};
      req.session.emailOtp = {
        email,
        otp,
        expiresAt: Date.now() + 10 * 60 * 1000 // 10 minutes
      };

      await sendOtpEmail(email, otp);

      logger.info('Email OTP sent', {
        component: 'oauth-controller',
        operation: 'send_email_otp',
        metadata: {
          email,
          otp_length: otp.length
        }
      });

      res.json({
        message: 'otp_sent_to_email',
        email,
        expiresIn: 600 // 10 minutes
      });
    } catch (err) {
      logger.error('Failed to send email OTP', err, {
        component: 'oauth-controller',
        operation: 'send_email_otp_error',
        metadata: { email }
      });
      res.status(500).json({ 
        message: 'failed_to_send_otp', 
        error: err.message 
      });
    }
  }

  /**
   * Verify email OTP
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async verifyEmailOtp(req, res) {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({ message: 'email_and_otp_required' });
    }

    try {
      const sessionOtp = req.session?.emailOtp;

      if (!sessionOtp || sessionOtp.email !== email || sessionOtp.otp !== otp) {
        return res.status(401).json({ message: 'invalid_otp' });
      }

      if (Date.now() > sessionOtp.expiresAt) {
        return res.status(401).json({ message: 'otp_expired' });
      }

      // Clear OTP from session
      delete req.session.emailOtp;

      // Create or find user
      let user = await User.findOne({ email });
      if (!user) {
        // Create Firebase user
        const firebaseUser = await admin.auth().createUser({
          email,
          emailVerified: true
        });

        user = await new User({
          firebase_uid: firebaseUser.uid,
          email,
          name: email.split('@')[0],
          status: 'active'
        }).save();

        logger.info('New user created via email OTP', {
          component: 'oauth-controller',
          operation: 'email_otp_user_creation',
          metadata: {
            user_id: user._id,
            email
          }
        });
      }

      // Generate tokens
      const jwt = require('jsonwebtoken');
      const accessToken = jwt.sign({ sub: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
      const refreshToken = jwt.sign({ sub: user._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });

      logger.info('Email OTP verification completed', {
        component: 'oauth-controller',
        operation: 'verify_email_otp_success',
        metadata: {
          user_id: user._id,
          email
        }
      });

      res.json({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: 3600,
        token_type: 'bearer',
        user: {
          _id: user._id,
          firebase_uid: user.firebase_uid,
          email: user.email,
          name: user.name,
          status: user.status
        }
      });
    } catch (err) {
      logger.error('Email OTP verification failed', err, {
        component: 'oauth-controller',
        operation: 'verify_email_otp_error',
        metadata: { email }
      });
      res.status(500).json({ 
        message: 'otp_verification_failed', 
        error: err.message 
      });
    }
  }
}

module.exports = new OAuthController();
